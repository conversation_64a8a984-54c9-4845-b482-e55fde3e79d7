using SmaTrendFollower.Console.Extensions;
using Alpaca.Markets;
using SmaTrendFollower.Models;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Diagnostics;
using SmaTrendFollower.Monitoring;

namespace SmaTrendFollower.Services;

/// <summary>
/// Enhanced signal generator with momentum and volatility filtering.
/// Combines parallel processing with intelligent market condition filtering.
/// </summary>
public sealed class EnhancedSignalGenerator : ISignalGenerator, IDisposable
{
    private readonly IMarketDataService _marketDataService;
    private readonly IUniverseProvider _universeProvider;
    private readonly ILiveStateStore _liveStateStore;
    private readonly IMomentumFilter _momentumFilter;
    private readonly IVolatilityFilter _volatilityFilter;
    private readonly IPositionSizer _positionSizer;
    private readonly ILogger<EnhancedSignalGenerator> _logger;
    private readonly ParallelOptions _parallelOptions;
    private readonly SemaphoreSlim _rateGate = new(20); // 20 concurrent calls ≈ 80-90 req/s

    public EnhancedSignalGenerator(
        IMarketDataService marketDataService,
        IUniverseProvider universeProvider,
        ILiveStateStore liveStateStore,
        IMomentumFilter momentumFilter,
        IVolatilityFilter volatilityFilter,
        IPositionSizer positionSizer,
        ILogger<EnhancedSignalGenerator> logger)
    {
        _marketDataService = marketDataService;
        _universeProvider = universeProvider;
        _liveStateStore = liveStateStore;
        _momentumFilter = momentumFilter;
        _volatilityFilter = volatilityFilter;
        _positionSizer = positionSizer;
        _logger = logger;

        _parallelOptions = new ParallelOptions
        {
            MaxDegreeOfParallelism = Environment.ProcessorCount,
            CancellationToken = CancellationToken.None
        };
    }

    public async Task<IEnumerable<TradingSignal>> RunAsync(int topN = 10)
    {
        var totalStopwatch = Stopwatch.StartNew();

        try
        {
            _logger.LogInformation("Starting enhanced signal generation with momentum and volatility filtering");

            // Step 1: Check market conditions first
            var marketVolatility = await _volatilityFilter.GetMarketVolatilityAsync();
            if (!marketVolatility.IsEligible)
            {
                _logger.LogWarning("Market conditions unfavorable for trading: {Reason}", marketVolatility.Reason);
                return Enumerable.Empty<TradingSignal>();
            }

            _logger.LogInformation("Market conditions favorable: {Regime} volatility, VIX {VIX:F1}",
                marketVolatility.Regime, marketVolatility.VixLevel);

            // Step 2: Get universe and fetch data
            var symbols = await _universeProvider.GetSymbolsAsync();
            var symbolList = symbols.Distinct().ToList(); // Remove duplicates

            var originalCount = symbols.Count();
            if (originalCount != symbolList.Count)
            {
                _logger.LogWarning("Removed {DuplicateCount} duplicate symbols from universe (original: {OriginalCount}, unique: {UniqueCount})",
                    originalCount - symbolList.Count, originalCount, symbolList.Count);
            }

            _logger.LogInformation("Screening {Count} symbols with enhanced filtering", symbolList.Count);

            // Log the symbols being processed
            _logger.LogInformation("Processing symbols: {Symbols}", string.Join(", ", symbolList));

            var symbolDataMap = await FetchDataInParallelAsync(symbolList);
            _logger.LogInformation("Fetched data for {Count}/{Total} symbols", symbolDataMap.Count, symbolList.Count);

            // Log which symbols were excluded due to insufficient data
            var excludedSymbols = symbolList.Except(symbolDataMap.Keys).ToList();
            if (excludedSymbols.Any())
            {
                _logger.LogWarning("Excluded {Count} symbols due to insufficient data: {Symbols}",
                    excludedSymbols.Count, string.Join(", ", excludedSymbols));
            }

            // Log which symbols were successfully processed
            _logger.LogInformation("Successfully processed symbols: {Symbols}", string.Join(", ", symbolDataMap.Keys));

            // Step 3: Apply filters and generate signals in parallel
            var filteredSignals = await ApplyFiltersAndGenerateSignalsAsync(symbolDataMap);

            // Step 4: Calculate position sizing for qualified signals
            var enhancedSignals = await CalculatePositionSizingAsync(filteredSignals);

            // Step 5: Final ranking and selection
            var finalSignals = enhancedSignals
                .Where(s => s.PositionSizing?.Shares > 0)
                .OrderByDescending(s => s.PositionSizing?.ConfidenceScore ?? 0)
                .ThenByDescending(s => s.SixMonthReturn)
                .Take(topN)
                .Select(s => new TradingSignal(s.Symbol, s.Price, s.Atr, s.SixMonthReturn))
                .ToList();

            totalStopwatch.Stop();

            _logger.LogInformation("Enhanced signal generation completed: {Count} signals from {Total} symbols in {ElapsedMs:F0}ms",
                finalSignals.Count, symbolList.Count, totalStopwatch.Elapsed.TotalMilliseconds);

            return finalSignals;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in enhanced signal generation");
            return Enumerable.Empty<TradingSignal>();
        }
    }

    /// <summary>
    /// Fetches market data for all symbols using parallel async I/O with rate limiting
    /// </summary>
    private async Task<ConcurrentDictionary<string, List<IBar>>> FetchDataInParallelAsync(List<string> symbols)
    {
        var dataStopwatch = Stopwatch.StartNew();
        var symbolDataMap = new ConcurrentDictionary<string, List<IBar>>();

        var fetchTasks = symbols.Select(async symbol =>
        {
            var sw = Stopwatch.StartNew();
            await _rateGate.WaitAsync();
            try
            {
                // TEMPORARILY DISABLED: Check if already signaled today
                // var today = DateTime.UtcNow.Date;
                // var alreadySignaled = await _liveStateStore.WasSignaledAsync(symbol, today);

                // if (alreadySignaled)
                // {
                //     _logger.LogDebug("Skipping {Symbol} - already signaled today", symbol);
                //     return;
                // }

                _logger.LogDebug("Processing {Symbol} - fetching data (signal check disabled for debugging)", symbol);

                var startDate = DateTime.UtcNow.AddDays(-300);
                var endDate = DateTime.UtcNow;

                var response = await _marketDataService.GetStockBarsAsync(symbol, startDate, endDate);
                var bars = response.Items.ToList();

                _logger.LogDebug("Fetched {Count} bars for {Symbol}", bars.Count, symbol);

                if (bars.Count >= 200)
                {
                    symbolDataMap[symbol] = bars;
                    _logger.LogDebug("Added {Symbol} to symbol data map with {Count} bars", symbol, bars.Count);
                }
                else
                {
                    _logger.LogWarning("Insufficient bars for {Symbol}: {Count} (need 200+)", symbol, bars.Count);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to fetch data for {Symbol}", symbol);
            }
            finally
            {
                _rateGate.Release();
                MetricsRegistry.SignalLatencyMs.Observe(sw.Elapsed.TotalMilliseconds);

                // Log slow signals
                if (sw.ElapsedMilliseconds > 500)
                {
                    _logger.LogWarning("Slow signal {Symbol} {Ms} ms", symbol, sw.ElapsedMilliseconds);
                }
            }
        });

        await Task.WhenAll(fetchTasks);
        dataStopwatch.Stop();

        _logger.LogDebug("Data fetching completed in {ElapsedMs:F0}ms", dataStopwatch.Elapsed.TotalMilliseconds);
        return symbolDataMap;
    }

    /// <summary>
    /// Applies momentum and volatility filters, then generates signals
    /// </summary>
    private async Task<List<EnhancedTradingSignalV2>> ApplyFiltersAndGenerateSignalsAsync(
        ConcurrentDictionary<string, List<IBar>> symbolDataMap)
    {
        var filterStopwatch = Stopwatch.StartNew();
        var signals = new ConcurrentBag<EnhancedTradingSignalV2>();
        var processedCount = 0;

        await Task.Run(() =>
        {
            Parallel.ForEach(symbolDataMap, _parallelOptions, kvp =>
            {
                var symbol = kvp.Key;
                var bars = kvp.Value;

                try
                {
                    // Apply momentum filter
                    var momentumEligible = _momentumFilter.IsEligible(symbol, bars);
                    if (!momentumEligible)
                    {
                        _logger.LogDebug("Momentum filter rejected {Symbol}", symbol);
                        return;
                    }

                    // Apply volatility filter (async call in sync context - not ideal but necessary for Parallel.ForEach)
                    var volatilityTask = _volatilityFilter.IsEligibleAsync(symbol, bars);
                    volatilityTask.Wait();
                    var volatilityEligible = volatilityTask.Result;

                    if (!volatilityEligible)
                    {
                        _logger.LogDebug("Volatility filter rejected {Symbol}", symbol);
                        return;
                    }

                    // Generate signal with enhanced data
                    var signal = GenerateEnhancedSignal(symbol, bars);
                    if (signal != null)
                    {
                        signals.Add(signal);
                    }

                    var count = Interlocked.Increment(ref processedCount);
                    if (count % 25 == 0)
                    {
                        _logger.LogDebug("Processed {Count}/{Total} symbols through filters",
                            count, symbolDataMap.Count);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to process {Symbol} through filters", symbol);
                }
            });
        });

        filterStopwatch.Stop();

        _logger.LogDebug("Filter processing completed in {ElapsedMs:F0}ms, {Count} signals passed filters",
            filterStopwatch.Elapsed.TotalMilliseconds, signals.Count);

        return signals.ToList();
    }

    /// <summary>
    /// Generates enhanced trading signal with additional analysis
    /// </summary>
    private EnhancedTradingSignalV2? GenerateEnhancedSignal(string symbol, List<IBar> bars)
    {
        try
        {
            var currentPrice = bars.Last().Close;

            // Calculate technical indicators
            var sma50 = (decimal)bars.GetSma50();
            var sma200 = (decimal)bars.GetSma200();
            var atr14 = (decimal)bars.GetAtr14();
            var sixMonthReturn = (decimal)bars.GetTotalReturn(126);

            // Enhanced analysis
            var momentumAnalysis = _momentumFilter.AnalyzeMomentum(symbol, bars);
            var volatilityAnalysis = _volatilityFilter.AnalyzeSymbolVolatility(symbol, bars);

            // Basic trend filter
            if (currentPrice <= sma50 || currentPrice <= sma200)
                return null;

            return new EnhancedTradingSignalV2(
                symbol,
                currentPrice,
                atr14,
                sixMonthReturn,
                momentumAnalysis,
                volatilityAnalysis,
                null // Position sizing will be calculated later
            );
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error generating enhanced signal for {Symbol}", symbol);
            return null;
        }
    }

    /// <summary>
    /// Calculates position sizing for qualified signals
    /// </summary>
    private async Task<List<EnhancedTradingSignalV2>> CalculatePositionSizingAsync(List<EnhancedTradingSignalV2> signals)
    {
        var sizingStopwatch = Stopwatch.StartNew();

        try
        {
            // Get account equity for position sizing
            var account = await _marketDataService.GetAccountAsync();
            var accountEquity = account.Equity;

            var sizingTasks = signals.Select(async signal =>
            {
                try
                {
                    var baseSignal = new TradingSignal(signal.Symbol, signal.Price, signal.Atr, signal.SixMonthReturn);
                    var positionSizing = await _positionSizer.CalculateSizingAsync(baseSignal, accountEquity ?? 0);

                    return signal with { PositionSizing = positionSizing };
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to calculate position sizing for {Symbol}", signal.Symbol);
                    return signal; // Return without position sizing
                }
            });

            var results = await Task.WhenAll(sizingTasks);

            sizingStopwatch.Stop();
            _logger.LogDebug("Position sizing completed in {ElapsedMs:F0}ms", sizingStopwatch.Elapsed.TotalMilliseconds);

            return results.ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in position sizing calculation");
            return signals; // Return signals without position sizing
        }
    }

    public void Dispose()
    {
        _rateGate?.Dispose();
    }
}

/// <summary>
/// Enhanced trading signal with additional analysis and position sizing
/// </summary>
public record EnhancedTradingSignalV2(
    string Symbol,
    decimal Price,
    decimal Atr,
    decimal SixMonthReturn,
    MomentumAnalysis MomentumAnalysis,
    SymbolVolatilityAnalysis VolatilityAnalysis,
    DynamicPositionSizing? PositionSizing
);
